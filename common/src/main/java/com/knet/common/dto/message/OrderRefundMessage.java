package com.knet.common.dto.message;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/10 14:04
 * @description: 订单退款消息体
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OrderRefundMessage implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "关联订单ItemId", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long orderItemId;

    @Schema(description = "关联订单项号", requiredMode = Schema.RequiredMode.REQUIRED, example = "ORDI-123456789012345678")
    private String orderItemNo;

    @Schema(description = "关联订单父id", requiredMode = Schema.RequiredMode.REQUIRED, example = "ORDI-123456789012345678")
    private String prentOrderId;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;

    @Schema(description = "退款金额", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal amount;

    @Schema(description = "事件时间戳", requiredMode = Schema.RequiredMode.REQUIRED)
    private long timestamp;
}
