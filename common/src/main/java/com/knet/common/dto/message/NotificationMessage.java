package com.knet.common.dto.message;

import com.knet.common.enums.MessageChannel;
import com.knet.common.enums.MessageType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/20 16:34
 * @description: 通知消息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationMessage implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 发送者唯一标识
     */
    @Schema(description = "发送者唯一标识", requiredMode = Schema.RequiredMode.REQUIRED)
    private String senderId;

    /**
     * 接收者唯一标识
     */
    @Schema(description = "接收者唯一标识", requiredMode = Schema.RequiredMode.REQUIRED)
    private String receiverId;

    /**
     * 消息内容
     */
    @Schema(description = "消息内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String content;

    /**
     * 消息类型
     *
     * @see MessageType
     */
    @Schema(description = "消息类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private MessageType msgType;

    /**
     * 根据消息类型获取默认发送通道
     *
     * @return 默认发送通道列表
     */
    public MessageChannel getDefaultChannel() {
        return switch (this.msgType) {
            case TEXT, SYSTEM_NOTICE, ORDER_NOTICE, PAYMENT_NOTICE -> MessageChannel.EMAIL;
            default -> MessageChannel.SMS;
        };
    }

    /**
     * 创建支付成功通知消息
     *
     * @param email   用户邮箱
     * @param orderId 订单号
     * @return 支付成功通知消息
     */
    public static NotificationMessage createPaymentNotice(String email, String orderId) {
        return NotificationMessage.builder()
                .senderId("<EMAIL>")
                .receiverId(email)
                .content("Your Order OrderNo: " + orderId + " is payment Successfully")
                .msgType(MessageType.PAYMENT_NOTICE)
                .build();
    }
}
