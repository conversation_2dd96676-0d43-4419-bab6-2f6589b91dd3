package com.knet.common.config;

import com.knet.common.aspect.OptimizedLoggableAspect;
import com.knet.common.filter.RequestContextFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/7/23
 * @description: 日志组件自动配置类
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "knet.logging.enabled", havingValue = "true", matchIfMissing = true)
public class LoggingConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public OptimizedLoggableAspect optimizedLoggableAspect() {
        log.info("Initializing OptimizedLoggableAspect");
        return new OptimizedLoggableAspect();
    }

    @Bean(name = "knetRequestContextFilter")
    @ConditionalOnMissingBean(name = "knetRequestContextFilter")
    public RequestContextFilter knetRequestContextFilter() {
        log.info("Initializing KNet RequestContextFilter");
        return new RequestContextFilter();
    }
}
