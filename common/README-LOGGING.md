# 优化的日志系统使用指南

## 概述

本项目已升级为统一的、高性能的日志系统，具有以下特性：

- ✅ **请求ID追踪**：每个请求自动生成唯一ID，支持分布式链路追踪
- ✅ **结构化日志格式**：使用emoji和结构化格式，提高可读性
- ✅ **敏感信息过滤**：自动过滤密码、token等敏感信息
- ✅ **性能优化**：避免切面崩溃，减少性能开销
- ✅ **统一管理**：所有服务共用同一套日志组件
- ✅ **可配置性**：支持灵活的日志配置选项

## 快速开始

### 1. 基本使用

在Controller或Service方法上添加`@Loggable`注解：

```java
@RestController
public class UserController {
    
    @Loggable("用户登录")
    @PostMapping("/login")
    public ResponseEntity<String> login(@RequestBody LoginRequest request) {
        // 业务逻辑
        return ResponseEntity.ok("登录成功");
    }
}
```

### 2. 高级配置

```java
@Loggable(
    value = "用户查询", 
    logArgs = true,           // 记录请求参数（默认true）
    logResult = true,         // 记录返回结果（默认true）
    logException = true,      // 记录异常信息（默认true）
    slowThreshold = 2000L     // 慢请求阈值，单位毫秒（默认1000ms）
)
@GetMapping("/users/{id}")
public User getUserById(@PathVariable Long id) {
    return userService.findById(id);
}
```

### 3. 敏感信息处理

对于包含敏感信息的接口，可以关闭参数或结果记录：

```java
@Loggable(
    value = "修改密码", 
    logArgs = false,    // 不记录包含密码的请求参数
    logResult = false   // 不记录可能包含敏感信息的返回结果
)
@PostMapping("/change-password")
public ResponseEntity<String> changePassword(@RequestBody ChangePasswordRequest request) {
    // 业务逻辑
    return ResponseEntity.ok("密码修改成功");
}
```

## 日志格式说明

### 请求开始日志
```
🚀 |== REQUEST ========================
   | REQUEST_ID : 123456789012
   | URL        : http://localhost:8080/api/users/123
   | Method     : GET UserController#getUserById
   | Desc       : 用户查询
   | Params     : [123]
   |===================================
```

### 正常请求完成日志
```
✅ |== RESPONSE =======================
   | REQUEST_ID : 123456789012
   | Method     : UserController#getUserById
   | Result     : {"id":123,"name":"张三","email":"***"}
   | Time       : 245 ms
   |===================================
```

### 慢请求日志
```
⚠️ |== SLOW REQUEST ===================
   | REQUEST_ID : 123456789012
   | Method     : UserController#getUserById
   | Result     : {"id":123,"name":"张三"}
   | Time       : 1500 ms (>1000ms)
   |===================================
```

### 超慢请求日志
```
🐌 |== VERY SLOW REQUEST ==============
   | REQUEST_ID : 123456789012
   | Method     : UserController#getUserById
   | Result     : {"id":123,"name":"张三"}
   | Time       : 6000 ms (>5000ms)
   |===================================
```

### 异常日志
```
❌ |== ERROR ==========================
   | REQUEST_ID : 123456789012
   | Method     : UserController#getUserById
   | Exception  : UserNotFoundException
   | Message    : 用户不存在
   | Time       : 123 ms
   |===================================
```

## 日志文件格式

在logback配置中，日志会包含以下MDC信息：

```
2025-07-23 14:30:25.123 [a1b2c3d4e5f6g7h8] [user-service] [user123] [http-nio-8080-exec-1] INFO  c.k.u.controller.UserController - 🚀 REQUEST_START | ...
```

格式说明：
- `[a1b2c3d4e5f6g7h8]`：请求ID
- `[user-service]`：服务名称
- `[user123]`：用户ID（如果有）
- `[http-nio-8080-exec-1]`：线程名

## 配置选项

### 应用配置

在`application.yml`中可以配置：

```yaml
knet:
  logging:
    enabled: true  # 是否启用日志组件（默认true）
```

### 敏感信息过滤

系统会自动过滤以下敏感字段：
- password, pwd
- token, authorization
- secret, key
- credential, auth
- session, cookie
- sign, signature
- cardno, cardnum, idcard
- phone, mobile, email

## 性能优化

1. **异常安全**：切面内部异常不会影响业务逻辑
2. **长度限制**：日志内容超过2000字符会自动截断
3. **延迟计算**：只有在需要时才进行JSON序列化
4. **MDC管理**：自动管理MDC上下文，避免内存泄漏

## 分布式链路追踪

系统支持分布式链路追踪：

1. **请求ID生成**：
   - 如果请求头中有`X-Request-ID`，则使用该ID
   - 如果没有，系统自动生成新的请求ID（时间戳+随机数）

2. **请求ID传递**：
   - 通过`X-Request-ID`请求头在服务间传递
   - 响应头中会返回请求ID，便于客户端追踪

3. **日志格式**：
   - 每个日志都包含请求ID，便于追踪完整的请求链路
   - 使用emoji区分不同类型的日志（🚀请求开始、✅正常完成、⚠️慢请求、🐌超慢请求、❌异常）

## 故障排查

### 常见问题

1. **日志没有请求ID**
   - 检查是否正确配置了logback-spring.xml
   - 确认RequestContextFilter是否正常工作

2. **敏感信息没有被过滤**
   - 检查字段名是否包含敏感关键词
   - 可以自定义LogSanitizer来添加更多过滤规则

3. **性能问题**
   - 调整slowThreshold阈值
   - 考虑关闭logArgs或logResult来减少序列化开销

## 最佳实践

1. **合理使用注解参数**：对于高频接口，考虑关闭不必要的日志记录
2. **设置合适的慢请求阈值**：根据业务特点调整slowThreshold
3. **监控日志文件大小**：定期清理或归档日志文件
4. **利用请求ID**：在业务代码中可以通过`RequestContext.getRequestId()`获取当前请求ID
5. **客户端传递请求ID**：前端可以在请求头中传递`X-Request-ID`来保持链路追踪
6. **日志可读性**：使用emoji和表格格式，便于快速识别不同类型的请求

## Gateway特殊说明

Gateway服务使用WebFlux，与其他基于Servlet的服务不同：

### Gateway日志格式
```
🌐 |== GATEWAY REQUEST ================
   | REQUEST_ID : GW123456789012
   | Method     : GET
   | URI        : http://localhost:7000/api/users/123
   | From       : *************
   | Time       : 1690123456789
   |===================================

✅ |== GATEWAY RESPONSE ===============
   | REQUEST_ID : GW123456789012
   | Method     : GET
   | URI        : /api/users/123
   | Status     : 200
   | Time       : 245 ms
   |===================================
```

### Gateway特性
- 使用`GatewayLoggingFilter`而不是`OptimizedLoggableAspect`
- 请求ID以"GW"开头，便于区分
- 自动记录所有通过网关的请求
- 支持请求ID在服务间传递

## 依赖配置

### AOP依赖
由于AOP依赖已从common模块移除，需要日志功能的服务需要自行添加：

```xml
<!-- Spring AOP 切面编程支持 - 用于日志切面 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-aop</artifactId>
</dependency>
```

### 已配置的服务
以下服务已自动配置AOP依赖：
- user-services
- order-services
- payment-services
- goods-services
- notification-services
- oauth-services
- delayed-services

### Gateway特殊处理
Gateway使用WebFlux，会自动排除Servlet相关的日志组件。

## 升级说明

从旧版本升级时：

1. 删除各服务中的旧LoggableAspect实现
2. 为需要日志功能的服务添加AOP依赖
3. 更新logback-spring.xml配置
4. Gateway会自动排除Servlet相关的日志组件
5. 重新编译和部署服务
6. 验证日志格式是否正确

## 架构说明

- **Gateway**: 使用WebFlux + GatewayLoggingFilter
- **其他服务**: 使用Servlet + OptimizedLoggableAspect + RequestContextFilter
- **请求ID传递**: Gateway生成请求ID，通过X-Request-ID头传递给下游服务

---

如有问题，请联系开发团队。
