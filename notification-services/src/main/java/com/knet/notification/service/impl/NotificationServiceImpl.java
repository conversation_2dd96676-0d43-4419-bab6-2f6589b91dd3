package com.knet.notification.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.knet.common.dto.message.NotificationMessage;
import com.knet.common.enums.MessageStatus;
import com.knet.notification.model.entity.SysMessage;
import com.knet.notification.service.INotificationService;
import com.knet.notification.service.ISysMessageDeliveryService;
import com.knet.notification.service.ISysMessageService;
import com.knet.notification.system.utils.GmailSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.knet.common.constants.NotificationConstants.KNET_B2B_ORDER_NOTIFICATION_SUB;

/**
 * <AUTHOR>
 * @date 2025/6/23 10:01
 * @description: 通知服务实现
 */
@Slf4j
@Service
public class NotificationServiceImpl implements INotificationService {
    @Resource
    private ISysMessageService sysMessageService;
    @Resource
    private GmailSender gmailSender;
    @Resource
    private ISysMessageDeliveryService sysMessageDeliveryService;

    @Override
    public void sendNotification(NotificationMessage notificationMessage) {
        SysMessage saveMessage = sysMessageService.saveMessage(notificationMessage);
        if (BeanUtil.isNotEmpty(saveMessage)) {
            //根据类型发送消息
            try {
                switch (notificationMessage.getDefaultChannel()) {
                    case EMAIL -> sendEmail(saveMessage);
                    default -> throw new RuntimeException("不支持的通知类型");
                }
            } catch (Exception e) {
                log.error("发送通知失败: {}", e.getMessage(), e);
            }
        }
    }

    private void sendEmail(SysMessage sysMessage) throws Exception {
        boolean sendText = gmailSender.sendText(sysMessage.getReceiverId(), KNET_B2B_ORDER_NOTIFICATION_SUB, sysMessage.getContent());
        if (sendText) {
            sysMessageDeliveryService.updateDeliveryStatus(sysMessage.getId(), sysMessage.getDefaultChannel(), MessageStatus.SUCCESS);
            log.info("发送通知成功: {}", sysMessage);
        } else {
            sysMessageDeliveryService.updateDeliveryStatus(sysMessage.getId(), sysMessage.getDefaultChannel(), MessageStatus.FAILED);
            log.error("发送通知 失败: {}", sysMessage);
        }
    }
}
