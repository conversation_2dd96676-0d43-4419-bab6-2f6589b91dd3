package com.knet.order.model.vo;

import com.knet.order.model.dto.req.CreateOrderRequest;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/4 14:48
 * @description: OrderItemDto
 */
@Data
public class OrderItemDataVo {
    private String sku;
    private String productName;
    private String imageUrl;
    private String size;
    private Integer quantity;
    private BigDecimal unitPrice;

    public static OrderItemDataVo createOrderItemDataDto(CreateOrderRequest.OrderItemRequest requestItem
            , CreateOrderRequest.SizeDetailRequest sizeDetailRequest
            , BigDecimal unitPrice) {
        OrderItemDataVo orderItemData = new OrderItemDataVo();
        orderItemData.setSku(requestItem.getSku());
        orderItemData.setProductName(requestItem.getProductName());
        orderItemData.setImageUrl(requestItem.getImageUrl());
        orderItemData.setSize(sizeDetailRequest.getSize());
        orderItemData.setQuantity(sizeDetailRequest.getQuantity());
        orderItemData.setUnitPrice(unitPrice);
        return orderItemData;
    }
}
