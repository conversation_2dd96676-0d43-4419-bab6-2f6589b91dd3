package com.knet.order.model.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.knet.common.base.BaseResponse;
import com.knet.common.enums.KnetOrderGroupStatus;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.common.utils.PriceFormatUtil;
import com.knet.order.model.dto.third.resp.OrderPaymentInfoResponse;
import com.knet.order.model.dto.third.resp.UserAddressDtoResp;
import com.knet.order.model.entity.SysOrderItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;
import java.util.List;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * <AUTHOR>
 * @date 2025/6/4 16:00
 * @description: 订单详情响应DTO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订单详情响应")
public class OrderDetailResponse extends BaseResponse {

    @Schema(description = "父订单ID")
    private String parentOrderId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "订单状态")
    private KnetOrderGroupStatus status;

    @Schema(description = "订单总金额（美元）")
    private String totalAmount;

    @Schema(description = "总商品数量")
    private Integer totalQuantity;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    @Schema(description = "订单创建时间")
    private Date createTime;

    /**
     * @see UserAddressDtoResp
     */
    @Schema(description = "收货地址信息")
    private UserAddressDtoResp shippingAddress;

    /**
     * @see OrderPaymentInfoResponse
     */
    @Schema(description = "支付信息")
    private OrderPaymentInfoResponse paymentInfo;

    @Schema(description = "订单明细项列表")
    private List<OrderItemDetailResponse> items;


    /**
     * 订单明细项详情响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "订单明细项详情响应")
    public static class OrderItemDetailResponse {

        @Schema(description = "订单项ID")
        private Long itemId;
        @Schema(description = "订单项-订单号")
        private String itemNo;
        @Schema(description = "商品SKU")
        private String sku;
        @Schema(description = "商品名称")
        private String productName;
        @Schema(description = "尺码")
        private String size;
        @Schema(description = "图片地址")
        private String imageUrl;
        @Schema(description = "物流单号")
        private String trackingNo;
        @Schema(description = "单价（美元）")
        private String price;
        @Schema(description = "数量")
        private Integer count;
        /**
         * @see KnetOrderItemStatus
         */
        @Schema(description = "子订单状态")
        private KnetOrderItemStatus status;

        @Schema(description = "是否可以取消", example = "true")
        private Boolean canCancel;

        public static OrderDetailResponse.OrderItemDetailResponse createDto(SysOrderItem item) {
            KnetOrderItemStatus displayStatus = KnetOrderItemStatus.displayStatus(item.getStatus());
            return OrderDetailResponse.OrderItemDetailResponse
                    .builder()
                    .itemId(item.getItemId())
                    .itemNo(item.getItemNo())
                    .sku(item.getSku())
                    .productName(item.getName())
                    .size(item.getSize())
                    .imageUrl(item.getImageUrl())
                    .price(PriceFormatUtil.formatPrice(item.getPrice()))
                    .count(item.getCount())
                    .status(displayStatus)
                    .canCancel(isCancelled(item.getStatus()))
                    .build();
        }

        /**
         * 根据订单状态设置订单是否可以取消，订单处于
         * 待支付、已支付、支付失败状态
         *
         * @return 是否可以取消
         */
        private static Boolean isCancelled(KnetOrderItemStatus status) {
            return switch (status) {
                case PENDING_PAYMENT, PAID, PAY_FAILED -> true;
                default -> false;
            };
        }
    }
}
