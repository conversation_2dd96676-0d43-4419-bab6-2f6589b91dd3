package com.knet.order.system.config;

import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/3/21 11:57
 * @description: RabbitConfig 绑定Confirm和Return回调
 **/

@Configuration
public class RabbitConfig {

    @Bean
    public TopicExchange orderExchange() {
        // 订单业务交换机
        return new TopicExchange("order-exchange", true, false);
    }

    @Bean
    public Queue orderQueue() {
        // 订单队列（带死信配置）
        return QueueBuilder
                .durable("order-queue." + "order-services")
                .withArgument("x-dead-letter-exchange", "order-service.dlx")
                .withArgument("x-dead-letter-routing-key", "order.order.*")
                .build();
    }

    @Bean
    public Binding orderBinding() {
        // 队列绑定
        return BindingBuilder
                .bind(orderQueue())
                .to(orderExchange())
                .with("order.*");
    }

    /**
     * 订单服务专用死信交换机
     */
    @Bean
    public DirectExchange orderDlxExchange() {
        return new DirectExchange("order-service.dlx", true, false);
    }

    /**
     * 订单服务订单死信队列
     */
    @Bean
    public Queue orderDlxQueue() {
        return QueueBuilder
                .durable("order-service.dlx.order.queue")
                .build();
    }

    /**
     * 订单服务订单死信绑定
     */
    @Bean
    public Binding orderDlxBinding() {
        return BindingBuilder
                .bind(orderDlxQueue())
                .to(orderDlxExchange())
                .with("order.order.*");
    }

    /**
     * 订单服务支付结果死信队列
     */
    @Bean
    public Queue orderPaymentResultDlxQueue() {
        return QueueBuilder
                .durable("order-service.dlx.payment-result.queue")
                .build();
    }

    /**
     * 订单服务支付结果死信绑定
     */
    @Bean
    public Binding orderPaymentResultDlxBinding() {
        return BindingBuilder
                .bind(orderPaymentResultDlxQueue())
                .to(orderDlxExchange())
                .with("order.payment.result.*");
    }

    /**
     * 订单服务库存补偿死信队列
     */
    @Bean
    public Queue orderInventoryCompensationDlxQueue() {
        return QueueBuilder
                .durable("order-service.dlx.inventory-compensation.queue")
                .build();
    }

    /**
     * 订单服务库存补偿死信绑定
     */
    @Bean
    public Binding orderInventoryCompensationDlxBinding() {
        return BindingBuilder
                .bind(orderInventoryCompensationDlxQueue())
                .to(orderDlxExchange())
                .with("order.inventory.failed.*");
    }

    /**
     * 订单服务库存锁定成功死信队列
     */
    @Bean
    public Queue orderInventoryLockSuccessDlxQueue() {
        return QueueBuilder
                .durable("order-service.dlx.inventory-lock-success.queue")
                .build();
    }

    /**
     * 订单服务库存锁定成功死信绑定
     */
    @Bean
    public Binding orderInventoryLockSuccessDlxBinding() {
        return BindingBuilder
                .bind(orderInventoryLockSuccessDlxQueue())
                .to(orderDlxExchange())
                .with("order.inventory.lock.success.*");
    }

    /**
     * 支付结果交换机
     */
    @Bean
    public TopicExchange paymentResultExchange() {
        return new TopicExchange("payment-result-exchange", true, false);
    }

    /**
     * 订单服务支付结果队列
     */
    @Bean
    public Queue paymentResultOrderQueue() {
        return QueueBuilder
                .durable("payment-result-queue.order-services")
                .withArgument("x-dead-letter-exchange", "order-service.dlx")
                .withArgument("x-dead-letter-routing-key", "order.payment.result.*")
                .build();
    }

    /**
     * 订单服务支付结果队列绑定
     */
    @Bean
    public Binding paymentResultOrderBinding() {
        return BindingBuilder
                .bind(paymentResultOrderQueue())
                .to(paymentResultExchange())
                .with("payment.result");
    }

    /**
     * 库存补偿交换机
     */
    @Bean
    public TopicExchange inventoryCompensationExchange() {
        return new TopicExchange("inventory-compensation-exchange", true, false);
    }

    /**
     * 订单服务库存补偿队列
     */
    @Bean
    public Queue inventoryCompensationOrderQueue() {
        return QueueBuilder
                .durable("inventory-compensation-queue.order-services")
                .withArgument("x-dead-letter-exchange", "order-service.dlx")
                .withArgument("x-dead-letter-routing-key", "order.inventory.failed.*")
                .build();
    }

    /**
     * 订单服务库存补偿队列绑定
     */
    @Bean
    public Binding inventoryCompensationOrderBinding() {
        return BindingBuilder
                .bind(inventoryCompensationOrderQueue())
                .to(inventoryCompensationExchange())
                .with("inventory.failed");
    }

    /**
     * 订单延迟交换机
     *
     * @return 订单延迟交换机
     */
    @Bean
    public TopicExchange delayedExchange() {
        // 订单业务交换机
        return new TopicExchange("order.delayed.exchange", true, false);
    }

    /**
     * 订单超时队列
     */
    @Bean
    public Queue orderTimeoutQueue() {
        return new Queue("timeout.order.queue.order-services", true);
    }

    /**
     * 绑定队列到延迟交换机
     */
    @Bean
    public Binding bindingTimeoutQueue() {
        return BindingBuilder.bind(orderTimeoutQueue())
                .to(delayedExchange())
                .with("timeout.order");
    }

    /**
     * 库存锁定成功交换机
     */
    @Bean
    public TopicExchange inventoryLockSuccessExchange() {
        return new TopicExchange("inventory-lock-success-exchange", true, false);
    }

    /**
     * 订单服务库存锁定成功队列
     */
    @Bean
    public Queue inventoryLockSuccessOrderQueue() {
        return QueueBuilder
                .durable("inventory-lock-success-queue.order-services")
                .withArgument("x-dead-letter-exchange", "order-service.dlx")
                .withArgument("x-dead-letter-routing-key", "order.inventory.lock.success.*")
                .build();
    }

    /**
     * 订单服务库存锁定成功队列绑定
     */
    @Bean
    public Binding inventoryLockSuccessOrderBinding() {
        return BindingBuilder
                .bind(inventoryLockSuccessOrderQueue())
                .to(inventoryLockSuccessExchange())
                .with("inventory.lock.success");
    }
}
