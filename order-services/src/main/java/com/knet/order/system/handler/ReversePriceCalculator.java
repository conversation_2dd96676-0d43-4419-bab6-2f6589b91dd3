package com.knet.order.system.handler;

import cn.hutool.core.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2025/7/1 16:19
 * @description: KnetGroup到手价计算实现
 */
@Slf4j
@Component
public class ReversePriceCalculator {

    /**
     * KnetGroup手续费
     */
    public static final Float B2B_FEE = 1.1f;

    /**
     * 获取卖家到手价
     *
     * @param finalDollar 最终价格（美元，如19.09美元）
     * @return 原始价格（美元，保留2位小数），若无解返回null
     */
    public static BigDecimal getSellerOwingPrice(BigDecimal finalDollar) {
        // 1. 最终价格小于11美元视为异常
        if (NumberUtil.isLess(finalDollar, new BigDecimal("11"))) {
            log.error("获取卖家到手价 异常价格过低,listingPrice{},sellerOwingPrice{}", finalDollar, 1);
            return BigDecimal.ZERO;
        }
        // 2. 原始价格 < 100美元（固定加价10美元）
        // 100美元 + 10美元 = 110美元
        if (NumberUtil.isLess(finalDollar, new BigDecimal("110"))) {
            BigDecimal result = NumberUtil.sub(finalDollar, 10).setScale(2, RoundingMode.DOWN);
            log.info("获取卖家到手价,listingPrice{},sellerOwingPrice{}", finalDollar, result);
            return result;
        }
        // 3. 原始价格 ≥ 100美元（加价10%）
        // 保留2位小数（向下取整）
        BigDecimal result = NumberUtil.div(finalDollar, B2B_FEE, 2, RoundingMode.DOWN);
        log.info("获取卖家到手价,listingPrice{},sellerOwingPrice{}", finalDollar, result);
        return result;
    }

    /**
     * 获取knetGroup 到手价
     *
     * @param finalDollar 最终价格（美元整数）
     * @return 原始价格（美元整数），若无解返回null
     */
    public static BigDecimal getKgOwningPrice(BigDecimal finalDollar) {
        return finalDollar;
    }
}
