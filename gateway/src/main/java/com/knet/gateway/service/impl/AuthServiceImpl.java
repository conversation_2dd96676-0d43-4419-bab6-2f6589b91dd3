package com.knet.gateway.service.impl;

import cn.hutool.core.util.StrUtil;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.gateway.service.IAuthService;
import com.knet.gateway.system.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.retry.Retry;

import javax.annotation.Resource;
import java.time.Duration;

import static com.knet.common.constants.UserServicesConstants.KNET_USER_TOKEN_PREFIX;

/**
 * <AUTHOR>
 * @date 2025/2/12 11:01
 * @description: 身份验证工具类
 */
@Slf4j
@Service
public class AuthServiceImpl implements IAuthService {
    @Resource
    private JwtUtil jwtUtil;
    @Resource
    private RedisCacheUtil redisCacheUtil;

    /**
     * 验证token
     *
     * @param token token
     * @return 验证结果
     */
    @Override
    public Mono<Boolean> validateToken(String token) {
        if (StrUtil.isBlank(token)) {
            return Mono.just(false);
        }
        // 从自定义线程池中获取调度器（改成同步的估计也是可以）延迟100ms
        return Mono.delay(Duration.ofMillis(100))
                .then(Mono.fromCallable(() -> getUserIdFromToken(token)).subscribeOn(Schedulers.boundedElastic()))
                .flatMap(userIdFromToken -> {
                    if (userIdFromToken == null) {
                        return Mono.just(false);
                    }
                    String redisKey = String.format(KNET_USER_TOKEN_PREFIX, userIdFromToken);
                    log.info("redisKey: {}", redisKey);
                    return validateRedisKey(redisKey)
                            .retryWhen(Retry.backoff(3, Duration.ofSeconds(1)))
                            .timeout(Duration.ofSeconds(2), Mono.just(false))
                            .onErrorReturn(false);
                })
                .onErrorResume(e -> {
                    log.error("在token解析或查询过程中发生错误", e);
                    return Mono.just(false);
                });
    }

    /**
     * 验证Redis中的键
     *
     * @param redisKey Redis键
     * @return 验证结果
     */
    private Mono<Boolean> validateRedisKey(String redisKey) {
        return Mono.fromCallable(() -> redisCacheUtil.hasKey(redisKey))
                // 单独的React线程池中执行阻塞的Redis查询
                .subscribeOn(Schedulers.boundedElastic());
    }

    /**
     * 获取用户id
     *
     * @param token token
     * @return 用户id
     */
    private String getUserIdFromToken(String token) throws RuntimeException {
        if (!jwtUtil.validateToken(token)) {
            throw new RuntimeException("Invalid token");
        }
        return jwtUtil.getUserIdFromToken(token);
    }
}
