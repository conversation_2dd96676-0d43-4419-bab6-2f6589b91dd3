package com.knet.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * <AUTHOR>
 * @date 2025/2/12 15:10
 * @description: Gateway 主启动类 - 优化版本
 */
@ComponentScan(
        basePackages = {"com.knet.gateway", "com.knet.common"},
        excludeFilters = {
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.knet\\.common\\.handler\\..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.knet\\.common\\.aspect\\.ModifyHeaderAspect"),
                // 排除基于Servlet的日志组件（Gateway使用WebFlux）
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.knet\\.common\\.aspect\\.OptimizedLoggableAspect"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.knet\\.common\\.filter\\.RequestContextFilter"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.knet\\.common\\.config\\.LoggingAutoConfiguration")
        }
)
@SpringBootApplication
@EnableDiscoveryClient
public class GatewayApplication {
    public static void main(String[] args) {
        //禁用Log4j JNDI
        System.setProperty("log4j2.formatMsgNoLookups", "true");
        SpringApplication.run(GatewayApplication.class, args);
        System.out.println("🚀 Gateway Service started successfully!");
    }

    /**
     * 配置负载均衡的WebClient
     */
    @Bean
    @LoadBalanced
    public WebClient.Builder webClientBuilder() {
        return WebClient.builder()
                // 设置最大内存大小为10MB
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024));
    }
}
