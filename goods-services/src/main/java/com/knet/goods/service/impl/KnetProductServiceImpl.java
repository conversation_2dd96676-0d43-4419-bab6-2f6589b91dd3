package com.knet.goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.context.UserContext;
import com.knet.common.enums.KnetCurrencyCode;
import com.knet.common.enums.ProductMark;
import com.knet.common.enums.ProductStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.PriceFormatUtil;
import com.knet.common.utils.RandomStrUtil;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.goods.mapper.KnetProductMapper;
import com.knet.goods.model.dto.req.*;
import com.knet.goods.model.dto.resp.*;
import com.knet.goods.model.dto.third.req.KnetGroupGetSkuSizePlatformPriceReq;
import com.knet.goods.model.dto.third.resp.KnetProductMarketDataVo;
import com.knet.goods.model.entity.KnetProduct;
import com.knet.goods.model.entity.SysUpdateSysSkuEvents;
import com.knet.goods.service.IKnetProductService;
import com.knet.goods.service.ISkuCacheService;
import com.knet.goods.service.ISysSkuService;
import com.knet.goods.service.IThirdApiService;
import com.knet.goods.system.handler.TempTableManager;
import com.knet.goods.system.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import static com.knet.common.constants.UserServicesConstants.KNET_SYS_SKU_CACHE_KEY_PREFIX;
import static com.knet.goods.model.entity.KnetProduct.initKnetProduct;


/**
 * <AUTHOR>
 * @date 2025/2/19 16:18
 * @description: Product service 实现类
 */
@Slf4j
@Service
public class KnetProductServiceImpl extends ServiceImpl<KnetProductMapper, KnetProduct> implements IKnetProductService {
    @Resource
    private KnetProductMapper baseMapper;
    @Resource
    private IThirdApiService thirdApiService;
    @Resource
    private RandomStrUtil randomStrUtil;
    @Resource
    private ISysSkuService sysSkuService;
    @Resource
    private JwtUtil jwtUtil;
    @Resource
    private ISkuCacheService skuCacheService;
    @Resource
    private TempTableManager tempTableManager;
    @Resource
    @Qualifier("goodsThreadPoolExecutor")
    private ThreadPoolExecutor goodsThreadPoolExecutor;

    @Override
    public IPage<ProductDtoResp> listProducts(ProductQueryRequest request) {
        QueryWrapper<KnetProduct> queryWrapper = new QueryWrapper<>();
        Page<KnetProduct> page = new Page<>(request.getQueryStartPage(), request.getPageSize());
        page = baseMapper.selectPage(page, queryWrapper);
        IPage<KnetProduct> productPage = baseMapper.selectPage(page, queryWrapper);
        return productPage.convert(KnetProduct::mapToProductDtoResp);
    }

    @Override
    public IPage<ProductBySkuDtoResp> queryProductGroupBySku(ProductQueryRequest request) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        if (ProductMark.ALL.equals(request.getMark())) {
            request.setMark(null);
        }
        IPage<ProductBySkuDtoResp> result = new Page<>(request.getQueryStartPage(), request.getPageSize());
        String accountFromToken = jwtUtil.getAccountFromToken(UserContext.getContext());
        request.setAccount(accountFromToken);
        //获取sku缓存
        Set<String> skus = skuCacheService.matchSkus(request.getSku());
        Set<String> remarks = skuCacheService.matchProductsByRemark(request.getSku());
        // 判断是否需要使用临时表优化
        boolean useTempTable = tempTableManager.shouldUseTempTable(skus.size(), remarks.size());
        Integer count;
        List<ProductBySkuDtoResp> products;
        if (useTempTable) {
            log.info("使用临时表优化查询，SKU 数量: {}, 商品名称 数量: {}", skus.size(), remarks.size());
            log.debug(tempTableManager.getConfigInfo());
            try {
                // 创建并填充临时表
                if (tempTableManager.createAndPopulateTempTable(skus, remarks)) {
                    log.info("使用分表策略执行优化查询");
                    count = baseMapper.queryProductGroupBySkuCountWithSeparateTables(request);
                    products = baseMapper.queryProductGroupBySkuWithSeparateTables(request);
                } else {
                    log.warn("临时表创建失败，回退到传统查询方式");
                    request.setSkus(new ArrayList<>(skus));
                    request.setRemarks(new ArrayList<>(remarks));
                    count = baseMapper.queryProductGroupBySkuCount(request);
                    products = baseMapper.queryProductGroupBySku(request);
                }
            } finally {
                tempTableManager.cleanupTempTable();
            }
        } else {
            log.info("使用传统查询方式，SKU数量: {}, 备注数量: {}", skus.size(), remarks.size());
            request.setSkus(new ArrayList<>(skus));
            request.setRemarks(new ArrayList<>(remarks));
            count = baseMapper.queryProductGroupBySkuCount(request);
            products = baseMapper.queryProductGroupBySku(request);
        }
        if (CollUtil.isNotEmpty(products)) {
            // 批量查询SKU备注信息，提高性能
            List<String> skuList = products.stream()
                    .map(ProductBySkuDtoResp::getSku)
                    .filter(StrUtil::isNotBlank)
                    .distinct()
                    .toList();
            if (CollUtil.isNotEmpty(skuList)) {
                Map<String, Map<String, String>> batchSkuRemarksMap = sysSkuService.getBatchSkuRemarksMap(skuList);
                products.forEach(product -> {
                    Map<String, String> skuRemarksMap = batchSkuRemarksMap.get(product.getSku());
                    if (MapUtil.isNotEmpty(skuRemarksMap)) {
                        product.setImg(skuRemarksMap.getOrDefault("imgUrl", ""));
                    }
                });
            }
        }
        result.setRecords(products);
        result.setTotal(count);
        result.setCurrent(request.getPageNo());
        stopWatch.stop();
        log.info("查询商品列表耗时: {} ms, 使用临时表: {}", stopWatch.getTotalTimeMillis(), useTempTable);
        return result;
    }

    @Override
    public List<ProductSkuSpecPriceDtoResp> queryProductDetails(ProductDetailsQueryRequest request) {
        if (StrUtil.isBlank(request.getAccount())) {
            String accountFromToken = jwtUtil.getAccountFromToken(UserContext.getContext());
            request.setAccount(accountFromToken);
        }
        List<ProductSkuSpecPriceDtoResp> dtoRests = baseMapper.queryProductDetails(request);
        if (CollUtil.isEmpty(dtoRests)) {
            return Collections.emptyList();
        }
        var skuInfoFuture = CompletableFuture.supplyAsync(
                () -> sysSkuService.getSkuRemarksMap(request.getSku()),
                goodsThreadPoolExecutor
        );
        var priceInfosFuture = CompletableFuture.supplyAsync(
                () -> baseMapper.queryProductDetailsPriceInfo(request),
                goodsThreadPoolExecutor
        );
        List<String> sizes = dtoRests.stream().map(ProductSkuSpecPriceDtoResp::getSpec).toList();
        CompletableFuture<List<KnetProductMarketDataVo>> marketDataFuture = CompletableFuture.supplyAsync(
                () -> {
                    try {
                        return thirdApiService.getProductMarketData(new KnetGroupGetSkuSizePlatformPriceReq(request.getSku(), sizes));
                    } catch (Exception e) {
                        log.error("调用kg获取商品市场数据失败, sku: {}, sizes: {}, 异常信息: {}", request.getSku(), sizes, e.getMessage(), e);
                        return Collections.emptyList();
                    }
                },
                goodsThreadPoolExecutor
        );
        try {
            Map<String, String> skuInfoMap = skuInfoFuture.get();
            if (MapUtil.isNotEmpty(skuInfoMap)) {
                dtoRests.forEach(dto -> {
                    dto.setImg(skuInfoMap.getOrDefault("imgUrl", ""));
                    dto.setRemarks(skuInfoMap.getOrDefault("remarks", ""));
                });
            }
            List<SpecPriceDto> priceInfos = priceInfosFuture.get();
            if (CollUtil.isNotEmpty(priceInfos)) {
                // 将价格信息按spec分组
                Map<String, List<SpecPriceDto>> priceInfoMap = priceInfos.stream()
                        .collect(Collectors.groupingBy(SpecPriceDto::getSpec));
                dtoRests.forEach(dtoRest -> {
                    List<SpecPriceDto> priceInfo = priceInfoMap.get(dtoRest.getSpec());
                    if (CollUtil.isNotEmpty(priceInfo)) {
                        dtoRest.setPriceInfo(priceInfo);
                    }
                });
            }
            List<KnetProductMarketDataVo> productMarketData = marketDataFuture.get();
            List<ProductSkuSpecPriceDtoResp> dtoResps = fillPlatformPrice(dtoRests, productMarketData);
            dtoResps = sortBySpec(dtoResps);
            return dtoResps;
        } catch (Exception e) {
            log.error("异步查询商品详情信息失败, sku: {}, 异常信息: {}", request.getSku(), e.getMessage(), e);
            throw new ServiceException("查询商品详情失败");
        }
    }

    /**
     * 填充平台价格
     *
     * @param dtoRests          商品详情
     * @param productMarketData 商品市场数据
     * @return 商品详情
     */
    List<ProductSkuSpecPriceDtoResp> fillPlatformPrice(List<ProductSkuSpecPriceDtoResp> dtoRests, List<KnetProductMarketDataVo> productMarketData) {
        if (CollUtil.isEmpty(dtoRests) || CollUtil.isEmpty(productMarketData)) {
            return dtoRests;
        }
        // 将市场数据转换为Map以提高查询效率
        Map<String, KnetProductMarketDataVo> marketDataMap = productMarketData.stream()
                .collect(Collectors.toMap(
                        KnetProductMarketDataVo::getSize,
                        data -> data,
                        (existing, replacement) -> existing,
                        () -> new HashMap<>(productMarketData.size())
                ));
        dtoRests.forEach(dtoRest -> {
            KnetProductMarketDataVo marketData = marketDataMap.get(dtoRest.getSpec());
            if (marketData == null) {
                return;
            }
            Optional.ofNullable(marketData.getStockX())
                    .map(KnetProductMarketDataVo.PlatformProductMarketDataVo::getLowestAskPrice)
                    .ifPresent(price -> dtoRest.setStockPrice(price.getAmount()));
            Optional.ofNullable(marketData.getGoat())
                    .map(KnetProductMarketDataVo.PlatformProductMarketDataVo::getLowestAskPrice)
                    .ifPresent(price -> dtoRest.setGoatPrice(price.getAmount()));
        });
        return dtoRests;
    }

    /**
     * 排序商品尺码
     *
     * @param originate 商品详情
     * @return 排序后的商品详情
     */
    List<ProductSkuSpecPriceDtoResp> sortBySpec(List<ProductSkuSpecPriceDtoResp> originate) {
        if (CollUtil.isEmpty(originate)) {
            return originate;
        }
        return originate.stream()
                .filter(item -> item.getSpec() != null)
                .sorted(Comparator.comparing(item -> {
                    String spec = item.getSpec();
                    try {
                        return Double.parseDouble(spec);
                    } catch (NumberFormatException e) {
                        // 非数字情况，使用字符串本身，但确保排序在数字之后
                        return Double.MAX_VALUE;
                    }
                }, Comparator.nullsLast(Double::compareTo)))
                .toList();
    }

    @Override
    public KnetProduct createByKnet(CreateKnetProductRequest.ProductDto request) {
        if (!KnetCurrencyCode.USD.equals(request.getCurrency())) {
            throw new ServiceException("暂时只支持美元");
        }
        KnetProduct knetProduct = initKnetProduct(request);
        knetProduct.setListingId(randomStrUtil.getProductId());
        if (KnetCurrencyCode.USD.equals(request.getCurrency())) {
            knetProduct.setPrice(request.getPrice());
        }
        return knetProduct;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void insertIgnoreBatch(List<KnetProduct> list) {
        baseMapper.insertIgnoreBatch(list);
    }

    @Override
    public List<String> queryByListingIds(List<String> listingIds) {
        LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(KnetProduct::getListingId, listingIds);
        List<KnetProduct> knetProducts = baseMapper.selectList(queryWrapper);
        if (CollUtil.isNotEmpty(knetProducts)) {
            return knetProducts.stream()
                    .map(KnetProduct::getOneId)
                    .toList();
        }
        return Collections.emptyList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> updateKnetProductForOffSale(List<OffSaleKnetProductRequest.ProductDto> products) {
        List<String> listingIds = products.stream()
                .map(OffSaleKnetProductRequest.ProductDto::getListingId)
                .distinct()
                .toList();
        if (CollUtil.isEmpty(listingIds)) {
            return Collections.emptyList();
        }
        log.info("商品下架:下架商品listingId{}", listingIds);
        LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .in(KnetProduct::getListingId, listingIds)
                .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                .set(KnetProduct::getStatus, ProductStatus.OFF_SALE);
        try {
            baseMapper.update(null, updateWrapper);
        } catch (Exception e) {
            throw new ServiceException("商品下架失败");
        }
        return listingIds;
    }

    /**
     * 校验商品是否下架
     *
     * @param listingIds listingIds
     * @return 下架商品
     */
    @Override
    public List<OffSaleKnetProductResp.ProductDto> getOffSaleListingIds(List<String> listingIds) {
        if (CollUtil.isEmpty(listingIds)) {
            return Collections.emptyList();
        }
        List<String> offSaleListingIds = baseMapper.selectList(new LambdaQueryWrapper<KnetProduct>()
                        .in(KnetProduct::getListingId, listingIds)
                        .eq(KnetProduct::getStatus, ProductStatus.OFF_SALE))
                .stream()
                .map(KnetProduct::getListingId)
                .toList();
        return offSaleListingIds.stream()
                .map(listingId -> new OffSaleKnetProductResp.ProductDto(listingId, true))
                .toList();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public OffSaleKnetProductResp.ProductDto processKnetProductPrice(UpdatePriceKnetProductRequest.ProductDto productDto) {
        if (!KnetCurrencyCode.USD.equals(productDto.getCurrency())) {
            throw new ServiceException("价格单位只能是美元");
        }
        LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(KnetProduct::getListingId, productDto.getListingId())
                .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                .set(KnetProduct::getPrice, productDto.getPrice());
        boolean updated = this.update(null, updateWrapper);
        if (!updated) {
            throw new ServiceException("更新商品价格失败，数据已经被其他操作修改");
        }
        return new OffSaleKnetProductResp.ProductDto(productDto.getListingId(), true);
    }

    @Override
    public List<String> queryProductBrands() {
        List<String> brands = RedisCacheUtil.cachedBrands();
        if (CollUtil.isEmpty(brands)) {
            List<String> dbBrands = sysSkuService.queryBrands();
            RedisCacheUtil.setBrands(dbBrands);
            return dbBrands;
        }
        return brands;
    }

    @Override
    public List<QueryKnetProductResp> queryKnetProductForApi(QueryKnetProductRequest request) {
        List<String> listingIds = request.getProducts().stream()
                .map(QueryKnetProductRequest.ProductDto::getListingId)
                .distinct()
                .toList();
        if (CollUtil.isEmpty(listingIds)) {
            return Collections.emptyList();
        }
        log.info("对外接口: 查询商品listingId{}", listingIds);
        LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .select(KnetProduct::getListingId
                        , KnetProduct::getCreateTime
                        , KnetProduct::getUpdateTime
                        , KnetProduct::getOneId
                        , KnetProduct::getSku
                        , KnetProduct::getSpec
                        , KnetProduct::getPrice
                        , KnetProduct::getStatus
                )
                .in(KnetProduct::getListingId, listingIds);
        List<KnetProduct> knetProducts = baseMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(knetProducts)) {
            return Collections.emptyList();
        }
        return knetProducts.stream()
                .map(KnetProduct::mapToQueryKnetProductResp)
                .toList();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void updateKnetProductForSysSkuInfo(List<SysUpdateSysSkuEvents> updateEvents) {
        if (CollUtil.isEmpty(updateEvents)) {
            return;
        }
        updateEvents.forEach(event -> {
            LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .eq(KnetProduct::getSku, event.getSku())
                    .set(StrUtil.isNotBlank(event.getBrand()), KnetProduct::getBrand, event.getBrand())
                    .set(StrUtil.isNotBlank(event.getRemarks()), KnetProduct::getRemarks, event.getRemarks());
            this.update(null, updateWrapper);
            String redisSysSkuKey = String.format(KNET_SYS_SKU_CACHE_KEY_PREFIX, event.getSku());
            if (RedisCacheUtil.hasKey(redisSysSkuKey)) {
                RedisCacheUtil.del(redisSysSkuKey);
            }
        });
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void setProductModifyMark(List<String> skus, ProductMark productMark) {
        if (CollUtil.isEmpty(skus)) {
            return;
        }
        LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .in(KnetProduct::getSku, skus)
                .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                .set(null != productMark, KnetProduct::getMark, productMark);
        try {
            baseMapper.update(null, updateWrapper);
        } catch (Exception e) {
            throw new ServiceException("更新商品标识失败");
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @Override
    public void resetProductMarkToCommon(ProductMark productMark) {
        LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(KnetProduct::getMark, productMark)
                .set(null != productMark, KnetProduct::getMark, ProductMark.COMMON);
        try {
            baseMapper.update(null, updateWrapper);
        } catch (Exception e) {
            throw new ServiceException("更新商品标识失败");
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public int updateExistingProductsToOffSale(List<String> oneIds) {
        if (CollUtil.isEmpty(oneIds)) {
            return 0;
        }
        try {
            // 查询已存在且状态为ON_SALE的oneId列表
            LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                    .in(KnetProduct::getOneId, oneIds)
                    .eq(KnetProduct::getStatus, ProductStatus.ON_SALE);
            List<KnetProduct> existingProducts = baseMapper.selectList(queryWrapper);
            if (CollUtil.isEmpty(existingProducts)) {
                log.info("没有找到需要下架的商品");
                return 0;
            }
            log.info("找到 {} 个需要下架的商品", existingProducts.size());
            // 将这些商品的状态设置为OFF_SALE
            LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .in(KnetProduct::getOneId, oneIds)
                    .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                    .set(KnetProduct::getStatus, ProductStatus.OFF_SALE);
            int updatedCount = baseMapper.update(null, updateWrapper);
            log.info("成功下架 {} 个商品", updatedCount);
            return updatedCount;
        } catch (Exception e) {
            log.error("下架已存在商品失败: {}", e.getMessage(), e);
            throw new ServiceException("下架已存在商品失败");
        }
    }

    /**
     * 获取本地所有上架状态商品的oneId
     *
     * @return 本地上架状态商品的oneId集合
     */
    @Override
    public Set<String> getLocalOnSaleOneIds() {
        LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .select(KnetProduct::getOneId)
                .eq(KnetProduct::getStatus, ProductStatus.ON_SALE);
        List<KnetProduct> onSaleProducts = baseMapper.selectList(queryWrapper);
        return onSaleProducts.stream()
                .map(KnetProduct::getOneId)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public boolean lockInventory(CheckAndLockInvRequest request) {
        if (CollUtil.isEmpty(request.getItems())) {
            return false;
        }
        request.getItems().forEach(item -> {
            LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
            long toCents = PriceFormatUtil.formatYuanToCents(item.getPrice());
            queryWrapper
                    .eq(KnetProduct::getSku, item.getSku())
                    .eq(KnetProduct::getPrice, toCents)
                    .eq(KnetProduct::getSpec, item.getSize())
                    .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                    .orderByDesc(KnetProduct::getCreateTime)
                    .last("LIMIT " + item.getCount());
            List<KnetProduct> productsToLock = baseMapper.selectList(queryWrapper);
            if (productsToLock.size() < item.getCount()) {
                log.error("商品: {} 尺码: {} 价格: {} 库存不足，需要{}个，实际只有{}个",
                        item.getSku(), item.getSize(), toCents, item.getCount(), productsToLock.size());
                throw new ServiceException("商品 " + item.getSku() + " 尺码 " + item.getSize() + " 价格 " + toCents + " 库存不足");
            }
            List<Long> idsToLock = productsToLock.stream().map(KnetProduct::getId).toList();
            try {
                LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper
                        .in(KnetProduct::getId, idsToLock)
                        .set(KnetProduct::getStatus, ProductStatus.LOCKED);
                int updatedCount = baseMapper.update(null, updateWrapper);
                log.info("商品锁定成功 - SKU: {}, 尺码: {}, 价格: {}, 锁定数量: {}, 商品ID列表: {}",
                        item.getSku(), item.getSize(), toCents, updatedCount, idsToLock);
            } catch (Exception e) {
                log.error("商品: {} 尺码: {} 价格: {} 锁定库存失败", item.getSku(), item.getSize(), toCents, e);
                throw new ServiceException("锁定库存失败");
            }
        });
        return true;
    }

    @Override
    public boolean checkInventory(CheckAndLockInvRequest request) {
        if (CollUtil.isEmpty(request.getItems())) {
            return false;
        }
        request.getItems().forEach(item -> {
            LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
            long toCents = PriceFormatUtil.formatYuanToCents(item.getPrice());
            queryWrapper
                    .eq(KnetProduct::getSku, item.getSku())
                    .eq(KnetProduct::getPrice, toCents)
                    .eq(KnetProduct::getSpec, item.getSize())
                    .eq(KnetProduct::getStatus, ProductStatus.ON_SALE);
            List<KnetProduct> knetProducts = baseMapper.selectList(queryWrapper);
            if (CollUtil.isEmpty(knetProducts)) {
                log.error("商品: {}, 尺码: {}, 价格:  {},  不存在", item.getSku(), item.getSize(), item.getPrice());
                throw new ServiceException("商品 " + item.getSku() + " 尺码 " + item.getSize() + "价格" + toCents + " 不存在");
            }
            if (knetProducts.size() < item.getCount()) {
                log.error("商品: {} 尺码: {}   价格:  {} 库存不足", item.getSku(), item.getSize(), toCents);
                throw new ServiceException("商品 " + item.getSku() + " 尺码 " + item.getSize() + "价格" + toCents + " 库存不足");
            }
        });
        return true;
    }

    @Override
    public List<QueryKnetProductResp> queryKnetProduct(InnerKnetProductRequest request) {
        LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(StrUtil.isNotBlank(request.getOneId()), KnetProduct::getOneId, request.getOneId())
                .eq(StrUtil.isNotBlank(request.getSku()), KnetProduct::getSku, request.getSku())
                .eq(StrUtil.isNotBlank(request.getSpec()), KnetProduct::getSpec, request.getSpec())
                .eq(BeanUtil.isNotEmpty(request.getStatus()), KnetProduct::getStatus, request.getStatus())
                .ne(StrUtil.isNotBlank(request.getAccount()), KnetProduct::getSource, request.getAccount());
        List<KnetProduct> knetProducts = baseMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(knetProducts)) {
            return Collections.emptyList();
        }
        return knetProducts.stream()
                .map(KnetProduct::mapToQueryKnetProductResp)
                .toList();
    }

    @Override
    public List<KnetProduct> getKnetProductsByListingIds(List<String> listingIds) {
        if (CollUtil.isEmpty(listingIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(KnetProduct::getListingId, listingIds);
        return baseMapper.selectList(queryWrapper);
    }
}
