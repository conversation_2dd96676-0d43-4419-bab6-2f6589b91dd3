package com.knet.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.knet.common.dto.message.PriceChangeMessage;
import com.knet.common.enums.ProductStatus;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.goods.mapper.SysPriceAggregationMapper;
import com.knet.goods.model.dto.req.PriceQueryRequest;
import com.knet.goods.model.dto.resp.PriceAggregationResp;
import com.knet.goods.model.entity.KnetProduct;
import com.knet.goods.service.IKnetProductService;
import com.knet.goods.service.IPriceAggregationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import static com.knet.common.constants.GoodsServicesConstants.PRICE_CACHE_EXPIRE;
import static com.knet.common.constants.GoodsServicesConstants.PRICE_CACHE_PREFIX;

/**
 * <AUTHOR>
 * @date 2025/7/9 16:45
 * @description: 价格聚合服务实现
 */
@Slf4j
@Service
public class PriceAggregationServiceImpl implements IPriceAggregationService {

    @Resource
    private SysPriceAggregationMapper priceAggregationMapper;
    @Resource
    private IKnetProductService knetProductService;
    @Resource
    @Qualifier("goodsThreadPoolExecutor")
    private ThreadPoolExecutor goodsThreadPoolExecutor;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handlePriceChangeEvent(PriceChangeMessage event) {
        log.info("处理价格变动事件: eventId={}, eventType={}, products={}",
                event.getEventId(), event.getEventType(), event.getProducts().size());
        try {
            // 按SKU+规格分组处理
            Map<String, List<PriceChangeMessage.ProductPriceInfo>> groupedProducts = event.getProducts()
                    .stream()
                    .collect(Collectors.groupingBy(p -> p.getSku() + ":" + p.getSpec()));
            for (Map.Entry<String, List<PriceChangeMessage.ProductPriceInfo>> entry : groupedProducts.entrySet()) {
                String[] skuSpec = entry.getKey().split(":");
                String sku = skuSpec[0];
                String spec = skuSpec.length > 1 ? skuSpec[1] : "";
                // 先删除缓存（双删策略第一次删除）
                deletePriceCache(sku, spec);
                // 重新计算价格聚合
                recalculateAndUpdatePriceAggregation(sku, spec);
                // 再次删除缓存（双删策略第二次删除）
                CompletableFuture.runAsync(() -> {
                    try {
                        // 延迟100ms
                        Thread.sleep(100);
                        deletePriceCache(sku, spec);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("缓存延迟删除被中断: sku={}, spec={}", sku, spec);
                    }
                }, goodsThreadPoolExecutor);
            }
            log.info("价格变动事件处理完成: eventId={}", event.getEventId());
        } catch (Exception e) {
            log.error("处理价格变动事件失败: eventId={}, error={}", event.getEventId(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public PriceAggregationResp queryPrice(String sku, String spec) {
        List<PriceAggregationResp> results = queryPriceAggregation(sku, spec);
        return CollUtil.isNotEmpty(results) ? results.get(0) : null;
    }

    @Override
    public List<PriceAggregationResp> batchQueryPrice(List<PriceQueryRequest.ProductQuery> skus) {
        if (CollUtil.isEmpty(skus)) {
            return Collections.emptyList();
        }
        // 按SKU分组，优化Hash缓存查询
        Map<String, List<PriceQueryRequest.ProductQuery>> skuGroupMap = skus.stream()
                .collect(Collectors.groupingBy(PriceQueryRequest.ProductQuery::getSku));
        List<CompletableFuture<List<PriceAggregationResp>>> futures = skuGroupMap.entrySet().parallelStream()
                .map(entry -> CompletableFuture.supplyAsync(() -> {
                    String sku = entry.getKey();
                    List<PriceQueryRequest.ProductQuery> skuProducts = entry.getValue();
                    // 批量从Hash缓存获取同一SKU的不同规格数据
                    String hashKey = PRICE_CACHE_PREFIX + sku;
                    Map<Object, Object> cachedData = RedisCacheUtil.hmget(hashKey);
                    List<PriceAggregationResp> results = new ArrayList<>();
                    List<PriceQueryRequest.ProductQuery> missedProducts = new ArrayList<>();
                    // 检查缓存命中情况
                    for (PriceQueryRequest.ProductQuery product : skuProducts) {
                        String spec = StrUtil.nullToEmpty(product.getSpec());
                        String cached = (String) cachedData.get(spec);
                        if (StrUtil.isNotBlank(cached)) {
                            List<PriceAggregationResp> cachedResults = JSON.parseArray(cached, PriceAggregationResp.class);
                            if (CollUtil.isNotEmpty(cachedResults)) {
                                results.add(cachedResults.get(0));
                            }
                        } else {
                            missedProducts.add(product);
                        }
                    }
                    // 查询缓存未命中的数据
                    for (PriceQueryRequest.ProductQuery product : missedProducts) {
                        PriceAggregationResp result = queryPrice(product.getSku(), product.getSpec());
                        if (result != null) {
                            results.add(result);
                        }
                    }
                    return results;
                }, goodsThreadPoolExecutor))
                .toList();
        return futures.stream()
                .map(CompletableFuture::join)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .toList();
    }

    @Override
    public List<PriceAggregationResp> queryPriceAggregation(String sku, String spec) {
        if (StrUtil.isBlank(sku)) {
            return Collections.emptyList();
        }
        // 使用Hash结构缓存：price:{sku} -> {spec: priceData}
        String hashKey = PRICE_CACHE_PREFIX + sku;
        String hashField = StrUtil.nullToEmpty(spec);
        String cachedData = (String) RedisCacheUtil.hget(hashKey, hashField);
        if (StrUtil.isNotBlank(cachedData)) {
            log.debug("从Hash缓存获取价格聚合数据: sku={}, spec={}", sku, spec);
            return JSON.parseArray(cachedData, PriceAggregationResp.class);
        }
        List<PriceAggregationResp> result = priceAggregationMapper.selectPriceAggregation(sku, spec);
        if (CollUtil.isNotEmpty(result)) {
            // 存储到Hash缓存
            RedisCacheUtil.hset(hashKey, hashField, JSON.toJSONString(result), PRICE_CACHE_EXPIRE);
            log.debug("价格聚合数据存储到Hash缓存: sku={}, spec={}", sku, spec);
        }
        return result;
    }

    @Override
    public void refreshPriceCache(String sku, String spec) {
        // 删除Hash缓存中的特定字段
        String hashKey = PRICE_CACHE_PREFIX + sku;
        String hashField = StrUtil.nullToEmpty(spec);
        RedisCacheUtil.hdel(hashKey, hashField);
        // 重新查询并缓存
        queryPriceAggregation(sku, spec);
        log.debug("刷新Hash价格缓存: sku={}, spec={}", sku, spec);
    }

    @Override
    public void deletePriceCache(String sku, String spec) {
        // 删除Hash缓存中的特定字段
        String hashKey = PRICE_CACHE_PREFIX + sku;
        String hashField = StrUtil.nullToEmpty(spec);
        RedisCacheUtil.hdel(hashKey, hashField);
        log.debug("删除Hash价格缓存: sku={}, spec={}", sku, spec);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void recalculateAndUpdatePriceAggregation(String sku, String spec) {
        log.info("重新计算价格聚合: sku={}, spec={}", sku, spec);
        try {
            LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                    .eq(KnetProduct::getSku, sku)
                    .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                    .gt(KnetProduct::getPrice, 0);
            if (StrUtil.isNotBlank(spec)) {
                queryWrapper.eq(KnetProduct::getSpec, spec);
            }
            List<KnetProduct> products = knetProductService.list(queryWrapper);
            if (CollUtil.isEmpty(products)) {
                log.info("没有找到在售商品，跳过价格聚合: sku={}, spec={}", sku, spec);
                return;
            }
            List<Long> prices = products.stream().map(KnetProduct::getPrice).toList();
            Long minPrice = prices.stream().min(Long::compareTo).orElse(0L);
            Long maxPrice = prices.stream().max(Long::compareTo).orElse(0L);
            // 更新聚合数据
            int updated = priceAggregationMapper.insertOrUpdatePriceAggregation(sku, spec, minPrice, maxPrice);
            log.info("价格聚合更新完成: sku={}, spec={}, minPrice={}, maxPrice={}, updated={}",
                    sku, spec, minPrice, maxPrice, updated);
        } catch (Exception e) {
            log.error("重新计算价格聚合失败: sku={}, spec={}, error={}", sku, spec, e.getMessage(), e);
            throw e;
        }
    }
}
