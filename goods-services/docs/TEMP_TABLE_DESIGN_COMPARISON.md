# 临时表设计方案对比

## 方案一：单表设计（当前方案）

### 表结构
```sql
CREATE TEMPORARY TABLE temp_sku_search (
    sku_value VARCHAR(500) NOT NULL,
    search_type ENUM('SKU', 'REMARK') NOT NULL DEFAULT 'SKU',
    PRIMARY KEY (sku_value, search_type),
    INDEX idx_sku_value (sku_value),
    INDEX idx_search_type (search_type)
) ENGINE=MEMORY
```

### 查询SQL
```sql
AND (
    EXISTS (SELECT 1 FROM temp_sku_search ts WHERE ts.sku_value = kp.sku_indexed AND ts.search_type = 'SKU')
    OR EXISTS (SELECT 1 FROM temp_sku_search ts WHERE ts.sku_value = kp.remarks AND ts.search_type = 'REMARK')
)
```

### 优缺点
✅ **优点**：
- 资源管理简单，只需维护一张表
- 代码逻辑统一，维护成本低
- 事务一致性好

❌ **缺点**：
- 查询需要额外的search_type条件判断
- 索引效率相对较低
- 数据类型混合，调试困难

---

## 方案二：分表设计（推荐方案）

### 表结构
```sql
-- SKU临时表
CREATE TEMPORARY TABLE temp_sku_list (
    sku_value VARCHAR(500) NOT NULL,
    PRIMARY KEY (sku_value)
) ENGINE=MEMORY

-- 备注临时表  
CREATE TEMPORARY TABLE temp_remark_list (
    remark_value VARCHAR(500) NOT NULL,
    PRIMARY KEY (remark_value)
) ENGINE=MEMORY
```

### 查询SQL
```sql
AND (
    EXISTS (SELECT 1 FROM temp_sku_list ts WHERE ts.sku_value = kp.sku_indexed)
    OR EXISTS (SELECT 1 FROM temp_remark_list tr WHERE tr.remark_value = kp.remarks)
)
```

### 优缺点
✅ **优点**：
- 索引效率高，单字段主键查询最优
- 数据类型清晰分离，便于调试和监控
- 查询条件简单，无需额外类型判断
- 扩展性好，可以为不同类型添加专属字段

❌ **缺点**：
- 需要管理两张临时表
- 代码复杂度稍高
- 占用更多的表结构元数据内存

---

## 性能对比分析

### 索引性能
| 方案 | 索引类型 | 查询效率 | 内存占用 |
|------|----------|----------|----------|
| 单表 | 复合主键(sku_value, search_type) | 中等 | 较低 |
| 分表 | 单字段主键(sku_value) | 高 | 较高 |

### 查询复杂度
| 方案 | WHERE条件 | 执行计划复杂度 | 优化器友好度 |
|------|-----------|----------------|--------------|
| 单表 | 需要search_type判断 | 中等 | 中等 |
| 分表 | 简单字段匹配 | 低 | 高 |

### 数据量影响
- **小数据量（<1000条）**：两种方案性能差异不大
- **中等数据量（1000-10000条）**：分表方案略优
- **大数据量（>10000条）**：分表方案明显优于单表

---

## 推荐方案选择

### 当前业务场景分析
根据业务特点，推荐使用**分表设计**：

1. **性能优先**：商品查询是高频操作，性能提升收益明显
2. **数据量大**：SKU匹配经常返回上万条数据，分表优势明显
3. **业务清晰**：SKU和REMARK是不同的业务概念，分离更合理

### 迁移建议
如果要从当前单表方案迁移到分表方案：

1. **渐进式迁移**：先保留单表方案，添加分表实现
2. **A/B测试**：通过配置开关对比两种方案的性能
3. **监控对比**：收集性能数据后再决定是否完全切换

### 配置化支持
```yaml
knet:
  goods:
    temp-table:
      strategy: SEPARATE_TABLES  # SINGLE_TABLE | SEPARATE_TABLES
      sku-table-name: temp_sku_list
      remark-table-name: temp_remark_list
```

---

## 结论

虽然当前的单表设计在代码维护性方面有优势，但考虑到：
- 商品查询的高频特性
- 大数据量场景下的性能要求
- 业务逻辑的清晰分离

**建议采用分表设计方案**，以获得更好的查询性能和更清晰的业务边界。
