package com.knet.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.enums.LanguageEnum;
import com.knet.common.enums.UserStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.user.mapper.SysUserMapper;
import com.knet.user.model.dto.req.UserEditRequest;
import com.knet.user.model.dto.req.UserQueryRequest;
import com.knet.user.model.dto.req.UserRechargeQueryRequest;
import com.knet.user.model.dto.req.UserSaveRequest;
import com.knet.user.model.dto.resp.UserInfoDtoResp;
import com.knet.user.model.dto.resp.UserRechargeRecordResp;
import com.knet.user.model.entity.SysUser;
import com.knet.user.model.entity.SysUserOperationRecord;
import com.knet.user.model.entity.SysUserRolerRel;
import com.knet.user.service.ISysUserOperationRecordService;
import com.knet.user.service.ISysUserRolerRelService;
import com.knet.user.service.ISysUserService;
import com.knet.user.service.IUserQueryService;
import com.knet.user.system.utils.UserRandomStrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/2/12 15:43
 * @description: 用户接口实现类
 */
@Slf4j
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService {
    @Resource
    private UserRandomStrUtil userRandomStrUtil;
    @Resource
    private ISysUserRolerRelService iSysUserRolerRelService;
    @Resource
    private ISysUserOperationRecordService iSysUserOperationRecordService;
    @Resource
    private IUserQueryService userQueryService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysUser createUser(UserSaveRequest userSaveRequest) {
        log.info("创建用户请求体:{}", userSaveRequest);
        if (StrUtil.hasBlank(userSaveRequest.getAccount())) {
            throw new ServiceException("用户账号名(非法)不能包含空格");
        }
        SysUser build = SysUser.builder()
                .uid(userRandomStrUtil.getUidStr())
                .account(userSaveRequest.getAccount())
                .nickName(userSaveRequest.getNickName())
                .email(userSaveRequest.getEmail())
                .language(userSaveRequest.getLanguage())
                .password(userRandomStrUtil.getEncryptPassword(userSaveRequest.getPassword()))
                .status(UserStatus.ENABLE)
                .build();
        try {
            this.save(build);
            SysUserRolerRel entity = new SysUserRolerRel(build.getId(), userSaveRequest.getRoleId());
            iSysUserRolerRelService.save(entity);
        } catch (Exception e) {
            throw new ServiceException("创建用户失败，用户账号重复");
        }
        log.info("创建用户成功:id {}", build.getId());
        clearUserListCache(null);
        return build;
    }

    @Override
    public IPage<UserInfoDtoResp> listUser(UserQueryRequest request) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotEmpty(request.getAccount())) {
            queryWrapper.likeRight(SysUser::getAccount, request.getAccount());
        }

        Page<SysUser> page = new Page<>(request.getPageNo(), request.getPageSize());
        IPage<SysUser> userPage = baseMapper.selectPage(page, queryWrapper);
        return userPage.convert(SysUser::mapToUserInfoDtoResp);
    }

    @Override
    public UserInfoDtoResp getUserById(Long id) {
        if (null == id || id <= 0) {
            throw new ServiceException("用户ID不能为空,或者用户ID不合法");
        }
        UserInfoDtoResp userInfoDtoResp = new UserInfoDtoResp();
        SysUser sysUser = this.getById(id);
        if (BeanUtil.isEmpty(sysUser)) {
            return null;
        }
        BeanUtils.copyProperties(sysUser, userInfoDtoResp);
        return userInfoDtoResp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUser(UserEditRequest request) {
        if (null == request.getId() || request.getId() <= 0) {
            throw new ServiceException("用户ID不能为空,或者用户ID不合法");
        }
        LambdaUpdateWrapper<SysUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(SysUser::getId, request.getId())
                .set(StrUtil.isNotBlank(request.getNickName()), SysUser::getNickName, request.getNickName())
                .set(StrUtil.isNotBlank(request.getEmail()), SysUser::getEmail, request.getEmail())
                .set(!LanguageEnum.EN_US.equals(request.getLanguage()), SysUser::getLanguage, request.getLanguage())
                .set(StrUtil.isNotBlank(request.getPassword()), SysUser::getPassword, userRandomStrUtil.getEncryptPassword(request.getPassword()))
        ;
        this.update(updateWrapper);
        clearUserListCache(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteUser(Long id) {
        log.info("删除用户请求体:{}", id);
        if (null == id || id <= 0) {
            throw new ServiceException("用户ID不能为空,或者用户ID不合法");
        }
        try {
            boolean removed = this.removeById(id);
            if (!removed) {
                log.error("删除用户失败,用户已被删除，或者用户不存在,id {}", id);
                throw new ServiceException("删除用户失败,用户已被删除，或者用户不存在 user_id:" + id);
            }
            //删除用户角色关联
            iSysUserRolerRelService.remove(new LambdaQueryWrapper<SysUserRolerRel>().eq(true, SysUserRolerRel::getUserId, id));
            clearUserListCache(null);
            log.info("删除用户成功:id {}", id);
            return true;
        } catch (Exception e) {
            log.error("删除用户失败,用户已被删除，或者用户不存在, id {}", id);
            throw new ServiceException("删除用户失败,用户已被删除，或者用户不存在 user_id:" + id);
        }
    }

    @Override
    public IPage<UserRechargeRecordResp> operationRecord(UserRechargeQueryRequest request) {
        IPage<SysUserOperationRecord> operationRecords = iSysUserOperationRecordService.queryList(request);
        return operationRecords.convert(record -> {
            String operationDesc = record.getOperationDesc();
            String amount = operationDesc.replaceAll("[^0-9.]", "");
            UserRechargeRecordResp resp = new UserRechargeRecordResp();
            BeanUtils.copyProperties(record, resp);
            resp.setOperationId(record.getOperationId());
            resp.setUserId(record.getUserId());
            resp.setAmount(amount);
            resp.setOperationType(record.getOperationType().getDesc());
            resp.setCreateTime(record.getCreateTime());
            resp.setAccount(userQueryService.getUserById(record.getUserId()).getAccount());
            return resp;
        });
    }

    /**
     * 清除用户订单列表缓存
     * 使用RedisCacheUtil的deleteByPattern方法进行模糊匹配删除
     */
    private void clearUserListCache(Long userId) {
        try {
            String cacheKeyPattern = userId == null ? "user-service:userList:*" : "user-service:userList:" + userId + ":*";
            RedisCacheUtil.deleteByPattern(cacheKeyPattern);
            log.info("已清除用户列表缓存，用户ID: {}, 缓存key模式: {}", userId, cacheKeyPattern);
        } catch (Exception e) {
            log.warn("清除用户列表缓存失败，用户ID: {}, 错误: {}", userId, e.getMessage());
        }
    }
}
