package com.knet.user.model.dto.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/21 15:00
 * @description: 购物车响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "购物车响应")
public class CartResponse {

    @Schema(description = "购物车ID")
    private Long cartId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "购物车商品项列表")
    private List<CartItemResponse> items;

    @Schema(description = "购物车商品总数量")
    private Integer totalQuantity;

    @Schema(description = "购物车商品总金额，美元")
    private String totalAmount;

    @Schema(description = "购物车商品平均金额，美元")
    private String avgAmount;

    /**
     * 初始化购物车响应
     *
     * @return CartResponse
     */
    public static CartResponse init(Long userId) {
        return CartResponse.builder()
                .userId(userId)
                .items(new ArrayList<>())
                .totalQuantity(0)
                .totalAmount("0.00")
                .avgAmount("0.00")
                .build();
    }
}
