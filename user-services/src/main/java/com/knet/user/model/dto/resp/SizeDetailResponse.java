package com.knet.user.model.dto.resp;

import com.knet.common.utils.PriceFormatUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/5/21 15:10
 * @description: 尺码明细响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "尺码明细响应")
public class SizeDetailResponse {

    @Schema(description = "尺码明细ID")
    private Long detailId;

    @Schema(description = "购物车商品项ID")
    private Long cartItemId;

    @Schema(description = "尺码值", example = "5")
    private String size;

    @Schema(description = "数量", example = "1")
    private Integer quantity;

    @Schema(description = "库存")
    private Integer stock;

    @Schema(description = "单价，美分", example = "9999")
    private String unitPrice;

    @Schema(description = "小计金额，美元", example = "9999")
    private String subtotal;

    @Schema(description = "是否选中 (0-未选中, 1-已选中)", example = "1")
    private Integer selected;

    /**
     * 创建尺码明细响应
     *
     * @param detail 购物车查询结果
     * @param itemId 购物车商品项ID
     * @return 尺码明细响应
     */
    public static SizeDetailResponse createSizeDetailResponse(CartQueryResult detail, Long itemId) {
        double subtotal = Double.parseDouble(detail.getUnitPrice()) * Long.valueOf(detail.getQuantity());
        return SizeDetailResponse
                .builder()
                .detailId(detail.getDetailId())
                .cartItemId(itemId)
                .size(detail.getSize())
                .quantity(detail.getQuantity())
                .unitPrice(detail.getUnitPrice())
                .subtotal(PriceFormatUtil.formatPrice(subtotal))
                .selected(detail.getDetailSelected())
                .build();
    }
}
