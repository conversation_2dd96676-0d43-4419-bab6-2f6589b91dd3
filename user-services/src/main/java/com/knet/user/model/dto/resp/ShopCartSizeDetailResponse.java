package com.knet.user.model.dto.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/6/17 17:11
 * @description: 购物车价格复合体
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ShopCartSizeDetailResponse {

    @Schema(description = "尺码明细ID")
    private Long detailId;

    @Schema(description = "购物车商品项ID")
    private Long cartItemId;

    @Schema(description = "尺码值", example = "5")
    private String size;

    @Schema(description = "购物车中添加数量", example = "1")
    private Integer quantity;

    @Schema(description = "库存数量")
    private Integer stock;

    @Schema(description = "单价，美分", example = "9999")
    private String unitPrice;

    @Schema(description = "是否选中 (0-未选中, 1-已选中)", example = "1")
    private Integer selected;
}
