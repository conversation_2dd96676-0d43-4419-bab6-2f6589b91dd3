package com.knet.user.model.dto.third.resp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.knet.common.base.BaseResponse;
import com.knet.common.config.PriceIntegerSerializer;
import com.knet.common.config.PriceRemainderSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/19 16:23
 * @description: 商品尺码价格数量平台dto
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class ProductSkuSpecPriceDtoResp extends BaseResponse {

    @Schema(description = "sku")
    private String sku;

    @Schema(description = "尺码")
    private String spec;

    @Schema(description = "商品图片")
    public String img;

    @Schema(description = "商品名称")
    public String remarks;

    @Schema(description = "价格区间 最低价格（单位：美元）", example = "10")
    @JsonSerialize(using = PriceIntegerSerializer.class)
    private String minPrice;

    @Schema(description = "价格区间 最高价格（单位：美元）", example = "10")
    @JsonSerialize(using = PriceIntegerSerializer.class)
    private String maxPrice;

    @Schema(description = "商品数量")
    private Integer total;

    @Schema(description = "平均价格（单位：美元）", example = "10.75")
    @JsonSerialize(using = PriceRemainderSerializer.class)
    private String avgPrice;

    @Schema(description = "总价格（单位：美元）", example = "10.75")
    @JsonSerialize(using = PriceRemainderSerializer.class)
    private String totalPrice;

    @Builder.Default
    @Schema(description = "stock价格（单位：美元）", example = "10.75")
    private String stockPrice = "0.00";

    @Builder.Default
    @Schema(description = "goat价格（单位：美元）", example = "10.75")
    private String goatPrice = "0.00";

    /**
     * 价格聚合信息
     *
     * @see SpecPriceDto
     */
    @Schema(description = "价格聚合信息")
    private List<SpecPriceDto> priceInfo;

}
